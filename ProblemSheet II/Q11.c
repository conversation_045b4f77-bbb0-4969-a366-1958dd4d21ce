/* 
========================================================================================
Name : Q11.c
Author: Subham Sourav
Description :    Write a program to ignore a SIGINT signal then reset the default action of the SIGINT signal -
use sigaction system call.
Date : 17-09-2024
========================================================================================
*/

#include <stdio.h>
#include <signal.h>
#include <unistd.h>
#include <stdlib.h>


int main() {
    struct sigaction ignore, def;

    ignore.sa_handler = SIG_IGN;  
    ignore.sa_flags = 0;          

    //  Ignore SIGINT
    if (sigaction(SIGINT, &ignore, NULL) == -1) {
        perror("Error ignoring");
    }

    printf("SIGINT is ignored. Press Ctrl+C\n");
    sleep(5);  

    def.sa_handler = SIG_DFL;      
    def.sa_flags = 0;               

    // Reset SIGINT
    if (sigaction(SIGINT, &def, NULL) == -1) {
        perror("Error resetting ");
       
    }

    printf("Press Ctrl+C again to terminate...\n");
    
    while (1) {
        printf("Running...\n");
        sleep(1);  
    }

    return 0;
}

/*
subham@subham-GF75:~/ProblemSheet II$ cc Q11.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out 
SIGINT is ignored. Press Ctrl+C
^C^C^C^C^C^C^C^C^C^C^C^CPress Ctrl+C again to terminate...
Running...
Running...
^C
*/