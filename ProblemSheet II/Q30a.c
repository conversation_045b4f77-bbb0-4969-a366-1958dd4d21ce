/* 
========================================================================================
Name : Q30.c
Author: Subham Sourav
Description :  Write a program to create a shared memory.
 a. write some data to the shared memory
 b. attach with O_RDONLY and check whether you are able to overwrite.
 c. detach the shared memory
 d. remove the shared memory
Date : 21-09-2024
========================================================================================
*/


#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/types.h>

#define SHM_SIZE 1024  

int main() {
    
    key_t key;
    int shmid;
    char *data;
    
    key = ftok("progfile", 90); 
    
    
    shmid = shmget(key, SHM_SIZE, 0644 | IPC_CREAT);
    printf("%d\n", shmid);
    
    data = (char *)shmat(shmid, (void *)0, 0);
    
    
    printf("Writing data to shared memory\n");
    strncpy(data, "Hello, Shared Memory!", SHM_SIZE);
    
    
    printf("Data in shared memory: %s\n", data);
    
    
        
    
    
    printf("Shared memory created and data written.\n");
     system("ipcs -m");
    return 0;
}
/*
subham@subham-GF75:~/ProblemSheet II$ cc Q30a.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out
32786
Writing data to shared memory
Data in shared memory: Hello, Shared Memory!
Shared memory created and data written.

------ Shared Memory Segments --------
key        shmid      owner      perms      bytes      nattch     status      
0x00000000 2          subham     600        524288     2          dest         
0x00000000 32779      subham     600        4194304    2          dest         
0x00000000 32784      subham     600        524288     2          dest         
0x5a0403e3 32786      subham     644        1024       1      
*/