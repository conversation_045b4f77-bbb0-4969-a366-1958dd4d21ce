/* 
========================================================================================
Name : Q29.c
Author: Subham Sourav
Description :  Write a program to remove the message queue.
Date : 20-09-2024
========================================================================================
*/

#include <stdio.h>
#include <stdlib.h>
#include <sys/ipc.h>
#include <sys/msg.h>

int main() {
    key_t key;
    int msgid;

    key = ftok("progfile", 65);

    msgid = msgget(key, 0666 | IPC_CREAT);


    msgctl(msgid, IPC_RMID, NULL) ;

    printf("Message queue successfully removed.\n");

    return 0;
}

/*
subham@subham-GF75:~/ProblemSheet II$ cc Q29.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out
Message queue successfully removed.
*/