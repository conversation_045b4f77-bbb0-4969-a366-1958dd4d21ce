 /* 
========================================================================================
Name : Q19.c
Author: Subham Sourav
Description : . Create a FIFO file by
 a. mknod command
 b. mkfifo command
 c. use strace command to find out, which command (mknod or mkfifo) is better.
 c. mknod system call
 d. mkfifo library function
Date : 17-09-2024
========================================================================================
*/

#include <stdio.h>
#include <stdlib.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>

int main() {
    
    
    const char *fifo_name = "my_fifo";

    // Create the FIFO file using mkfifo library function
    mkfifo(fifo_name, 0666) ;

    printf("FIFO file '%s' created successfully.\n", fifo_name);
    return 0;
}

/*
subham@subham-GF75:~/ProblemSheet II$ cc Q19e.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out 
FIFO file 'my_fifo' created successfully.
*/