 /* 
========================================================================================
Name : Q20.c
Author: Subham Sourav
Description : Write two programs so that both can communicate by FIFO -Use one way communication
Date : 17-09-2024
========================================================================================
*/


// receiver.c
#include <stdio.h>
#include <stdlib.h>
#include <fcntl.h>    
#include <sys/stat.h> 
#include <unistd.h>   



int main() {
    
       const char *FIFO_PATH= "my_fifo";

    int fd = open(FIFO_PATH, O_RDONLY);

    char b[1024];

    read(fd, b, sizeof(b));

    printf("Received message: %s\n", b);

    close(fd);

    return 0;
}
/*
subham@subham-GF75:~/ProblemSheet II$ cc Q20b.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out 
Received message: Hello from the sender!
*/
