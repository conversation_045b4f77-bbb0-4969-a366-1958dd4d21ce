/* 
========================================================================================
Name : Q9.c
Author: Subham Sourav
Description :    . Write a program to ignore a SIGINT signal then reset the default action of the SIGINT
signal - Use signal system call.
Date : 17-09-2024
========================================================================================
*/

#include <stdio.h>
#include <signal.h>
#include <unistd.h>


int main() {
    // Step 1: Ignore SIGINT
    printf("Ignoring SIGINT\n");
    signal(SIGINT, SIG_IGN);
    //signal(SIGINT,handle);

    sleep(5);

    // Step 2: Reset SIGINT 
    printf("Resetting SIGINT \n");
    signal(SIGINT, SIG_DFL);
    sleep(10);

    printf("\n END \n");

    return 0;
}

/*
subham@subham-GF75:~/ProblemSheet II$ cc Q9.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out 
Ignoring SIGINT
^C^C^C^C^C^C^C^CResetting SIGINT 
^C
*/