/* 
========================================================================================
Name : Q24.c
Author: Subham Sourav
Description :  Write a program to create a message queue and print the key and message queue id.
Date : 19-09-2024
========================================================================================
*/

#include <stdio.h>
#include <stdlib.h>
#include <sys/ipc.h>
#include <sys/msg.h>

int main() {
    key_t key;
    int msgid;

    key = ftok("Notes.txt", 80); 
   

    msgid = msgget(key, 0666 | IPC_CREAT);
    

    printf("Message Queue Key: %d\n", key);
    printf("Message Queue ID: %d\n", msgid);

    return 0;
}
/*
ubham@subham-GF75:~/ProblemSheet II$ cc Q24.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out
Message Queue Key: 1090782179
Message Queue ID: 1
*/

