/* 
========================================================================================
Name : Q26.c
Author: Subham Sourav
Description :   Write a program to send messages to the message queue. Check $ipcs -q
Date : 19-09-2024
========================================================================================
*/

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/ipc.h>
#include <sys/msg.h>

struct message {
    long msg_type;
    char msg_text[100];
};

int main() {
    struct message msg;
    key_t key;
    int msgid;

    key = ftok("progfile", 65);

    msgid = msgget(key, 0666 | IPC_CREAT);
    

    
    msg.msg_type = 4;
    strcpy(msg.msg_text, "Hello, this is a test message.");

    msgsnd(msgid, &msg, sizeof(msg), 0) ;

    printf("Message sent: %s\n", msg.msg_text);

    return 0;
}
/*
subham@subham-GF75:~/ProblemSheet II$ cc Q26.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out
Message sent: Hello, this is a test message.
*/