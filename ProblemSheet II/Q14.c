/* 
========================================================================================
Name : Q14.c
Author: Subham Sourav
Description :  Write a simple program to create a pipe, write to the pipe, read from pipe and display on
the monitor.
Date : 17-09-2024
========================================================================================
*/

#include <stdio.h>
#include <unistd.h>
#include <string.h>

int main() {
    int pipefds[2];  
    char w_msg[] = " Content is pushed to the pipe!";  
    char r_msg[100];  

    
    if (pipe(pipefds) == -1) {
        perror("Pipe creation failed");
        return 1;
    }

    
    printf("Writing to the pipe: %s\n", w_msg);
    write(pipefds[1], w_msg, strlen(w_msg) + 1);  

    
    read(pipefds[0], r_msg, sizeof(r_msg)); 

    
    printf("Reading from the pipe: %s\n", r_msg);

    
    close(pipefds[0]);  
    close(pipefds[1]); 

    return 0;
}

/*
subham@subham-GF75:~/ProblemSheet II$ cc Q14.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out 
Writing to the pipe:  Content is pushed to the pipe!
Reading from the pipe:  Content is pushed to the pipe!
*/