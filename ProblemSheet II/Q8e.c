/* 
========================================================================================
Name : Q8.c
Author: Subham Sourav
Description :    Write a separate program using signal system call to catch the following signals.
 a. SIGSEGV
 b. SIGINT
 c. SIGFPE
 d. SIGALRM (use alarm system call)
 e. SIGALRM (use setitimer system call)
 f. SIGVTALRM (use setitimer system call)
 g. SIGPROF (use setitimer system call)
Date : 16-09-2024
========================================================================================
*/

#include <stdio.h>
#include <stdlib.h>
#include <sys/time.h>
#include <signal.h>
#include <unistd.h>

void handler(int SignalNumber) {
    printf("Timer expired! Signal No: %d\n", SignalNumber);
}

int main() {
    struct itimerval timer;
    
    signal(SIGALRM, handler);
    
    timer.it_value.tv_sec = 10;    
    timer.it_value.tv_usec = 10;   

    timer.it_interval.tv_sec = 10;    
    timer.it_interval.tv_usec = 10;  

    if (setitimer(ITIMER_REAL, &timer, NULL) == -1) {
        perror("Error setting timer");
       
    }

    // Loop forever waiting for signals to arrive
    while (1) {
        pause();  // Wait for the signal
    }

    return 0;
}


//signal number for SIGALRM in system using the command:kill -l

/*
subham@subham-GF75:~/ProblemSheet II$ cc Q1a.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out 
Timer expired! Signal No: 14
Timer expired! Signal No: 14
*/