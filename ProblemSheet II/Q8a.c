/* 
========================================================================================
Name : Q8.c
Author: Subham Sourav
Description :    Write a separate program using signal system call to catch the following signals.
 a. SIGSEGV
 b. SIGINT
 c. SIGFPE
 d. SIGALRM (use alarm system call)
 e. SIGALRM (use setitimer system call)
 f. SIGVTALRM (use setitimer system call)
 g. SIGPROF (use setitimer system call)
Date : 16-09-2024
========================================================================================
*/
#include <stdio.h>
#include <signal.h>
#include <stdlib.h>


void handle(int s) {
    printf("Caught (Segmentation Fault)\n");
    exit(0);
}

int main() {
    
    signal(SIGSEGV, handle);

    // (dereference a NULL pointer)
    int *ptr = NULL;
    *ptr = 42;
  

    return 0;
}

/*
subham@subham-GF75:~/ProblemSheet II$ cc Q8a.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out 
Caught (Segmentation Fault)
*/