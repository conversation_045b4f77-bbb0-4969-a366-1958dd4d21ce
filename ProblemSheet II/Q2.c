/* 
========================================================================================
Name : Q2.c
Author: Subham Sourav
Description :  Write a program to print the system resource limits. Use getrlimit system call. 
Date : 15-09-2024
========================================================================================
*/
#include <stdio.h>
#include <sys/time.h>
#include <sys/resource.h>

void print_limit(int resource, const char *name) {
    struct rlimit limit;
    if (getrlimit(resource, &limit) == 0) {
        printf("%-30s: Soft limit = %ld, Hard limit = %ld\n", name, (long)limit.rlim_cur, (long)limit.rlim_max);
    } else {
        perror("getrlimit");
    }
}

int main() {
    printf("System Resource Limits:\n");

    print_limit(RLIMIT_CPU, "CPU time");
    print_limit(RLIMIT_FSIZE, "File size");
    print_limit(RLIMIT_DATA, "Data segment size");
    print_limit(RLIMIT_STACK, "Stack size");
    print_limit(RLIMIT_CORE, "Core file size");
    print_limit(RLIMIT_RSS, "Resident set size");
    print_limit(RLIMIT_NPROC, "Number of processes");
    print_limit(RLIMIT_NOFILE, "Number of open files");
    print_limit(RLIMIT_MEMLOCK, "Locked-in-memory size");
    print_limit(RLIMIT_AS, "Address space size");
      print_limit(RLIMIT_NICE, "Nice Value");

    return 0;
}

// see prlimit also


/*
subham@subham-GF75:~/ProblemSheet II$ cc Q2.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out 
System Resource Limits:
CPU time                      : Soft limit = -1, Hard limit = -1
File size                     : Soft limit = -1, Hard limit = -1
Data segment size             : Soft limit = -1, Hard limit = -1
Stack size                    : Soft limit = 8388608, Hard limit = -1
Core file size                : Soft limit = 0, Hard limit = -1
Resident set size             : Soft limit = -1, Hard limit = -1
Number of processes           : Soft limit = 63053, Hard limit = 63053
Number of open files          : Soft limit = 1048576, Hard limit = 1048576
Locked-in-memory size         : Soft limit = 2075250688, Hard limit = 2075250688
Address space size            : Soft limit = -1, Hard limit = -1
Nice Value                    : Soft limit = 0, Hard limit = 0

*/