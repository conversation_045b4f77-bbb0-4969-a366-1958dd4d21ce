/* 
========================================================================================
Name : Q31.c
Author: Subham Sourav
Description :  Write a program to create a semaphore and initialize value to the semaphore.
 a. create a binary semaphore
 b. create a counting semaphore
Date : 21-09-2024
========================================================================================
*/

#include <stdio.h>
#include <unistd.h>
#include <sys/sem.h>
#include <sys/types.h>
#include <sys/ipc.h>
#include <stdlib.h>
// Define the union semun
union semun {
    int val;               // Value for SETVAL
    struct semid_ds *buf;  // Buffer for IPC_STAT, IPC_SET
    unsigned short *array; // Array for GETALL, SETALL
};

int main(void) {
    union semun arg;
    key_t key;
    int semid;

    // Generate a unique key
    key = ftok(".", 'a');
    

    // Create a semaphore set with one semaphore
    semid = semget(key, 1, IPC_CREAT | 0644);
   

    // Initialize the semaphore value
    arg.val = 1; // 1 for binary semaphore
    semctl(semid, 0, SETVAL, arg) ;

    printf("Binary semaphore created and initialized to 1.\n");
    system("ipcs -s ");
    return 0;
}
/*
subham@subham-GF75:~/ProblemSheet II$ cc Q31a.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out
Binary semaphore created and initialized to 1.

------ Semaphore Arrays --------
key        semid      owner      perms      nsems     
0x61044110 2          subham     644        1    
*/