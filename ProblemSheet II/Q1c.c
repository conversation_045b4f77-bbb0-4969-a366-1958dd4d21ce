/* 
========================================================================================
Name : Q1c.c
Author: Subham Sourav
Description : Write a separate program (for each time domain) to set a interval timer in 10sec and
10micro second
 a. ITIMER_REAL
 b. ITIMER_VIRTUAL
 c. ITIMER_PROF
Date : 15-09-2024
========================================================================================
*/

#include <stdio.h>
#include <stdlib.h>
#include <sys/time.h>
#include <signal.h>
#include <unistd.h>

void handler(int SignalNumber) {
    printf("Timer expired! Signal number: %d\n", SignalNumber);
}

int main() {
    struct itimerval timer;

    // Setting up the signal handler using signal cmd
    signal(SIGPROF, handler);

    
    timer.it_value.tv_sec = 10;      
    timer.it_value.tv_usec = 10;     
    timer.it_interval.tv_sec = 10;    
    timer.it_interval.tv_usec = 10;   

    
    if (setitimer(ITIMER_PROF, &timer, NULL) == -1) {
        perror("Error setting timer");
    
    }

    
    printf("Timer started for 10 sec & 10 microsec. Press Ctrl+C to stop.\n");

    // Busy-wait loop
    while (1) {
        // perform a system call to consume kernel time
         getpid();  // A simple system call to simulate kernel-mode activity

    }

    return 0;
}

/*
subham@subham-GF75:~/ProblemSheet II$ cc Q1c.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out 
Timer started for 10 sec & 10 microsec. Press Ctrl+C to stop.
Timer expired! Signal number: 27
Timer expired! Signal number: 27
*/
