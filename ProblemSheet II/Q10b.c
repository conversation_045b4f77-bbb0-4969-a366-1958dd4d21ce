/* 
========================================================================================
Name : Q10.c
Author: Subham Sourav
Description :    Write a separate program using sigaction system call to catch the following signals.
 a. SIGSEGV
 b. SIGINT
 c. SIGFPE
Date : 17-09-2024
========================================================================================
*/

#include <stdio.h>
#include <signal.h>
#include <stdlib.h>
#include<unistd.h>

void handle(int s, siginfo_t *info, void *ucontext) {
    printf("Caught SIGINT \n");
    printf("Signal number: %d\n", s);
    printf("Fault address: %p\n", info->si_addr);  // Address that caused the fault
    exit(1);
}

int main() {
    struct sigaction sa;

    
    sa.sa_flags = SA_SIGINFO;  // Use the extended signal handler (with siginfo)
    sa.sa_sigaction = handle;  

    // Clear the signal set (block no signals while handling SIGSEGV)
    sigemptyset(&sa.sa_mask);

    
    if (sigaction(SIGINT, &sa, NULL) == -1) {
        perror("sigaction"); 
    }
    
   while (1) {
        printf("Running... Press Ctrl+C to send SIGINT\n");
        sleep(1);  
    }

    return 0;
}

/*
subham@subham-GF75:~/ProblemSheet II$ ./a.out 
Running... Press Ctrl+C to send SIGINT
Running... Press Ctrl+C to send SIGINT
Running... Press Ctrl+C to send SIGINT
^CCaught SIGINT 
Signal number: 2
Fault address: (nil)
*/