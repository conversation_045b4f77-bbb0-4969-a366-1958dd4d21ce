/* 
========================================================================================
Name : Q30.c
Author: Subham Sourav
Description :  Write a program to create a shared memory.
 a. write some data to the shared memory
 b. attach with O_RDONLY and check whether you are able to overwrite.
 c. detach the shared memory
 d. remove the shared memory
Date : 21-09-2024
========================================================================================
*/

// remove_memory.c
#include <stdio.h>
#include <stdlib.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/types.h>

int main() {
    key_t key;
    int shmid;

    key = ftok("progfile", 90); 

    

    shmid = shmget(key, 1024, 0644);
    

    shmctl(shmid, IPC_RMID, NULL);

    printf("Shared memory removed successfully.\n");
  system("ipcs -m");

    return 0;
}
/*
subham@subham-GF75:~/ProblemSheet II$ cc Q30d.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out
Shared memory removed successfully.

------ Shared Memory Segments --------
key        shmid      owner      perms      bytes      nattch     status      
0x00000000 2          subham     600        524288     2          dest         
0x00000000 32779      subham     600        4194304    2          dest         
0x00000000 32784      subham     600        524288     2          dest
*/