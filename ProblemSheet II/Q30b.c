/* 
========================================================================================
Name : Q30.c
Author: Subham Sourav
Description :  Write a program to create a shared memory.
 a. write some data to the shared memory
 b. attach with O_RDONLY and check whether you are able to overwrite.
 c. detach the shared memory
 d. remove the shared memory
Date : 21-09-2024
========================================================================================
*/

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/types.h>

#define SHM_SIZE 1024 

int main() {
    key_t key;
    int shmid;
    char *data;

    key = ftok("progfile", 90); 

    

    shmid = shmget(key, SHM_SIZE, 0644);

    data = (char *)shmat(shmid, (void *)0, SHM_RDONLY);
   
    printf("Data in shared memory (read-only): %s\n", data);

    printf("Attempted to overwrite \n");
    strncpy(data, "Overwrite attempt", SHM_SIZE);  
    printf("Data after overwrite attempt: %s\n", data);

shmdt(data) ;
  system("ipcs -m");

    return 0;
}
/*
subham@subham-GF75:~/ProblemSheet II$ cc Q30b.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out
Data in shared memory (read-only): Hello, Shared Memory!
Attempted to overwrite 
Segmentation fault (core dumped)
*/