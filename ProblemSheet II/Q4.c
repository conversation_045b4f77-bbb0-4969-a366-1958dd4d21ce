/* 
========================================================================================
Name : Q4.c
Author: Subham Sourav
Description :  Write a program to measure how much time is taken to execute 100 getppid ( )
system call. Use time stamp counter.  
Date : 15-09-2024
========================================================================================
*/


#include <stdio.h>
#include <time.h>
#include <unistd.h>


int main() {
    struct timespec start, end;
    int i;

    //  starting time
    clock_gettime(CLOCK_MONOTONIC, &start);

   
    for (i = 0; i < 100; i++) {
        getppid();
    }

    //  ending time
    clock_gettime(CLOCK_MONOTONIC, &end);

    
    long s = end.tv_sec - start.tv_sec;
    long ns = end.tv_nsec - start.tv_nsec;

    

    // Print the time taken in nanoseconds
    printf("Time taken for 100 getppid() calls: %ld seconds and %ld nanoseconds\n", s, ns);

    return 0;
}

/*
subham@subham-GF75:~/ProblemSheet II$ cc Q4.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out 
Time taken for 100 getppid() calls: 0 seconds and 253251 nanoseconds

*/