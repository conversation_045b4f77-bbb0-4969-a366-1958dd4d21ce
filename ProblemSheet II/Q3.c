/* 
========================================================================================
Name : Q3.c
Author: Subham Sourav
Description :  Write a program to set (any one) system resource limit. Use setrlimit system call. 
Date : 15-09-2024
========================================================================================
*/

#include <stdio.h>
#include <stdlib.h>
#include <sys/time.h>
#include <sys/resource.h>

void print() {
    struct rlimit limit;
    if (getrlimit(RLIMIT_STACK, &limit) == 0) 
        printf("Current Stack Limit: Soft limit = %ld bytes, Hard limit = %ld bytes\n",
               limit.rlim_cur, limit.rlim_max);

}

int main() {
    struct rlimit limit;

    // current stack limit
    printf("Before setting new stack limit:\n");
    print();


    limit.rlim_cur = 16 * 1024 * 1024; // 16 MB
    limit.rlim_max = -1;   // Infinity

    if (setrlimit(RLIMIT_STACK, &limit) == 0) {
        printf("Stack limit set successfully.\n");
    } else {
        perror("setrlimit");
    }

    // new stack limit
    printf("new stack limit:\n");
    print();

    return 0;
}
// see prlimit also



/*
subham@subham-GF75:~/ProblemSheet II$ cc Q3.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out 
Before setting new stack limit:
Current Stack Limit: Soft limit = 8388608 bytes, Hard limit = -1 bytes
Stack limit set successfully.
new stack limit:
Current Stack Limit: Soft limit = 16777216 bytes, Hard limit = -1 bytes
*/
