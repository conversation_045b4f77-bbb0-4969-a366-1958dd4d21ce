/* 
========================================================================================
Name : Q10.c
Author: Subham Sourav
Description :    Write a separate program using sigaction system call to catch the following signals.
 a. SIGSEGV
 b. SIGINT
 c. SIGFPE
Date : 17-09-2024
========================================================================================
*/

#include <stdio.h>
#include <signal.h>
#include <stdlib.h>
#include<unistd.h>

void handle(int s, siginfo_t *info, void *ucontext) {
    printf("Caught SIGSEGV \n");
    printf("Signal number: %d\n", s);
    printf("Fault address: %p\n", info->si_addr);  // Address that caused the fault
    exit(1);
}

int main() {
    struct sigaction sa;

    
    sa.sa_flags = SA_SIGINFO;  // Use the extended signal handler (with siginfo)
    sa.sa_sigaction = handle;  

    // Clear the signal set (block no signals while handling SIGSEGV)
    sigemptyset(&sa.sa_mask);

    
    if (sigaction(SIGSEGV, &sa, NULL) == -1) {
        perror("sigaction"); 
    }
    
    int *ptr = NULL;
    *ptr = 42;  

    return 0;
}

/*
subham@subham-GF75:~/ProblemSheet II$ cc Q10.c 
subham@subham-GF75:~/ProblemSheet II$ ./a.out 
Caught SIGSEGV 
Signal number: 11
Fault address: (nil)
*/