/* 
========================================================================================
Name : Q8.c
Author: Subham Sourav
Description :    Write a separate program using signal system call to catch the following signals.
 a. SIGSEGV
 b. SIGINT
 c. SIGFPE
 d. SIGALRM (use alarm system call)
 e. SIGALRM (use setitimer system call)
 f. SIGVTALRM (use setitimer system call)
 g. SIGPROF (use setitimer system call)
Date : 16-09-2024
========================================================================================
*/
#include <stdio.h>
#include <signal.h>
#include <stdlib.h>
#include<unistd.h>

void handle(int s) {
    printf("Caught SIGINT\n");
   exit(0);
}

int main() {
    signal(SIGINT, handle);

    while (1) {
        printf("Running... Press Ctrl+C to send signal\n");
        sleep(1);
    }

    return 0;
}

/*
subham@subham-GF75:~/ProblemSheet II$ cc Q8b.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out 
Running... Press Ctrl+C to send signal
Running... Press Ctrl+C to send signal
Running... Press Ctrl+C to send signal
^CCaught SIGINT
*/