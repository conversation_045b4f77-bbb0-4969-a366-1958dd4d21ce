/* 
========================================================================================
Name : Q6.c
Author: Subham Sourav
Description :  Write a simple program to create three threads.
Date : 15-09-2024
========================================================================================
*/


#include <stdio.h>
#include <pthread.h>
#include <unistd.h>



void* thread_function(void* arg) {
    int thread_id = *(int*)arg;  
    printf("THREAD %d: HellO, I aM a THREAD!\n", thread_id);
    
    return NULL;
}

int main() {
    pthread_t threads[3];  // A thread IDs
    int thread_arg[3];    

    
    for (int i = 0; i < 3; i++) {
        thread_arg[i] = i + 1;  
        if (pthread_create(&threads[i], NULL, thread_function, &thread_arg[i]) != 0) {
            perror("Failed to create thread");
            return 1;
        }
    }

    // Wait for all threads to finish
    for (int i = 0; i < 3; i++) {
        pthread_join(threads[i], NULL);
    }

    printf("FINISHED \n");

    return 0;
}

/*
subham@subham-GF75:~/ProblemSheet II$ cc Q6.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out 
Thread 1: Hello, I am a thread!
Thread 2: Hello, I am a thread!
Thread 3: Hello, I am a thread!
*/