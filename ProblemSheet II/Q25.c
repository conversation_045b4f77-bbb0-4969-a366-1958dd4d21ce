/* 
========================================================================================
Name : Q25.c
Author: Subham Sourav
Description :   Write a program to print a message queue's (use msqid_ds and ipc_perm structures)
 a. access permission
 b. uid, gid
 c. time of last message sent and received
 d. time of last change in the message queue
 d. size of the queue
 f. number of messages in the queue
 g. maximum number of bytes allowed
 h. pid of the msgsnd and msgrcv
Date : 19-09-2024
========================================================================================
*/

#include <stdio.h>
#include <stdlib.h>
#include <sys/ipc.h>
#include <sys/msg.h>
#include <sys/types.h>
#include <time.h>

int main() {
    key_t key;
    int msqid;
    struct msqid_ds buf;

    // Generate an IPC key
    key = ftok("progfile", 65);  // Replace "progfile" with your file

    // Get the message queue identifier
    msqid = msgget(key, 0666 | IPC_CREAT);
    if (msqid == -1) {
        perror("msgget");
        exit(EXIT_FAILURE);
    }

    // Get message queue information using msgctl
    if (msgctl(msqid, IPC_STAT, &buf) == -1) {
        perror("msgctl");
        exit(EXIT_FAILURE);
    }

    // Rephrased print statements
    printf("Permissions for this queue: %o\n", buf.msg_perm.mode);
    printf("User ID of owner: %d, Group ID of owner: %d\n", buf.msg_perm.uid, buf.msg_perm.gid);
    printf("Last message was sent at: %s", ctime(&buf.msg_stime));
    printf("Last message was received at: %s", ctime(&buf.msg_rtime));
    printf("Last modification of the queue occurred at: %s", ctime(&buf.msg_ctime));
    printf("Current number of bytes in the queue: %lu\n", buf.msg_cbytes);
    printf("Total messages in the queue: %lu\n", buf.msg_qnum);
    printf("Max number of bytes allowed in this queue: %lu\n", buf.msg_qbytes);
    printf("Process ID of the last message sender: %d\n", buf.msg_lspid);
    printf("Process ID of the last message receiver: %d\n", buf.msg_lrpid);

    return 0;
}

/*
subham@subham-GF75:~/ProblemSheet II$ cc Q25.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out 
Permissions for this queue: 666
User ID of owner: 1000, Group ID of owner: 1000
Last message was sent at: Thu Jan  1 05:30:00 1970
Last message was received at: Thu Jan  1 05:30:00 1970
Last modification of the queue occurred at: Mon Sep 23 19:21:12 2024
Current number of bytes in the queue: 0
Total messages in the queue: 0
Max number of bytes allowed in this queue: 16384
Process ID of the last message sender: 0
Process ID of the last message receiver: 0
*/