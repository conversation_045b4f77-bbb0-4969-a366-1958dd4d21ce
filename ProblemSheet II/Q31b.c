/* 
========================================================================================
Name : Q31.c
Author: Subham Sourav
Description :  Write a program to create a semaphore and initialize value to the semaphore.
 a. create a binary semaphore
 b. create a counting semaphore
Date : 21-09-2024
========================================================================================
*/

#include <stdio.h>
#include <unistd.h>
#include <sys/sem.h>
#include <sys/types.h>
#include <sys/ipc.h>
#include<stdlib.h>
// Defining the semun union as it's often absent in some environments
union semun {
    int val;                    // Value for SETVAL
    struct semid_ds *buf;        // Buffer for IPC_STAT, IPC_SET
    unsigned short int *array;   // Array for GETALL, SETALL
};

int main(void) {
    union semun arg;
    int key, semid;

    // Generate a unique key for the semaphore
    key = ftok(".", 'a');
    if (key == -1) {
        perror("ftok");
        return 1;
    }

    // Create the semaphore set with 1 semaphore and permissions 0644
    semid = semget(key, 1, IPC_CREAT | 0644);
    if (semid == -1) {
        perror("semget");
        return 1;
    }

    // Set the semaphore value for counting semaphore
    arg.val = 5;  // Set the initial value of the semaphore to 5 (for counting semaphore)
    
    // Set the semaphore value using SETVAL
    if (semctl(semid, 0, SETVAL, arg) == -1) {
        perror("semctl");
        return 1;
    }

    printf("Semaphore initialized with a counting value of %d\n", arg.val);
        system("ipcs -s");

    return 0;
}
/*
subham@subham-GF75:~/ProblemSheet II$ cc Q31a.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out
Binary semaphore created and initialized to 1.

------ Semaphore Arrays --------
key        semid      owner      perms      nsems     
0x61044110 2          subham     644        1    
*/