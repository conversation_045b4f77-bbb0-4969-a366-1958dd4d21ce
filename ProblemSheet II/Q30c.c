/* 
========================================================================================
Name : Q30.c
Author: Subham Sourav
Description :  Write a program to create a shared memory.
 a. write some data to the shared memory
 b. attach with O_RDONLY and check whether you are able to overwrite.
 c. detach the shared memory
 d. remove the shared memory
Date : 21-09-2024
========================================================================================
*/


// detach_memory.c
#include <stdio.h>
#include <stdlib.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/types.h>

#define SHM_SIZE 1024

int main() {
    key_t key;
    int shmid;
    char *data;

    key = ftok("progfile", 90); 


    shmid = shmget(key, SHM_SIZE, 0644);
    
    data = (char *)shmat(shmid, (void *)0, 0);
    

    shmdt(data) ;

    printf("Shared memory detached successfully.\n");
 system("ipcs -m");
    return 0;
}
/*
subham@subham-GF75:~/ProblemSheet II$ cc Q30c.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out
Shared memory detached successfully.

------ Shared Memory Segments --------
key        shmid      owner      perms      bytes      nattch     status      
0x00000000 2          subham     600        524288     2          dest         
0x00000000 32779      subham     600        4194304    2          dest         
0x00000000 32784      subham     600        524288     2          dest         
0x5a0403e3 32786      subham     644        1024       0                       

*/