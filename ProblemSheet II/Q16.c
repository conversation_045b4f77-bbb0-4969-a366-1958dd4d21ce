/* 
========================================================================================
Name : Q16.c
Author: Subham Sourav
Description : . Write a program to send and receive data from parent to child vice versa. Use two way
communication.
Date : 17-09-2024
========================================================================================
*/


#include <stdio.h>
#include <unistd.h>
#include <string.h>
#include <sys/types.h>
#include <stdlib.h>

int main() {
    int pipefd1[2];      // Pipe 1: Parent -> Child
    int pipefd2[2];      // Pipe 2: Child -> Parent
    pid_t pid;
    char parent_msg[] = "Hello from parent";
    char child_msg[] = "Hello from child";
    char read_msg[100];
    

    
    if (pipe(pipefd1) == -1 || pipe(pipefd2) == -1) {
        perror("Pipe creation failed");
        exit(0);
    }

    
    pid = fork();

    if (pid == -1) {
        perror("Fork failed");
        exit(0);
    }

    if (pid == 0) {
        // Child process

        
        close(pipefd1[1]);  
        close(pipefd2[0]);  

        
    read(pipefd1[0], read_msg, sizeof(read_msg));
            
            printf("Child: Received message from parent: '%s'\n", read_msg);
        

        close(pipefd1[0]);

        write(pipefd2[1], child_msg, strlen(child_msg) + 1);

        close(pipefd2[1]);

    } else {
        // Parent process

        close(pipefd1[0]);  
        close(pipefd2[1]);  

        
        write(pipefd1[1], parent_msg, strlen(parent_msg) + 1);

        
        close(pipefd1[1]);

        
        read(pipefd2[0], read_msg, sizeof(read_msg)); 
            printf("Parent: Received message from child: '%s'\n", read_msg);
        

        close(pipefd2[0]);
    }

    return 0;
}

/*
subham@subham-GF75:~/ProblemSheet II$ cc Q16.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out 
Child: Received message from parent: 'Hello from parent'
Parent: Received message from child: 'Hello from child'
*/