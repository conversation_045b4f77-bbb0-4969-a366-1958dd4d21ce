/* 
========================================================================================
Name : Q15.c
Author: Subham Sourav
Description : . Write a simple program to send some data from parent to the child process
Date : 17-09-2024
========================================================================================
*/

#include <stdio.h>
#include <unistd.h>
#include <string.h>
#include <sys/types.h>
#include <stdlib.h>

int main() {
    int pipefds[2];    
    pid_t pid;         
    char w_msg[] = "Hello from parent to child!"; 
    char r_msg[100];   


    if (pipe(pipefds) == -1) {
        perror("<PERSON><PERSON> failed");
    }

    pid = fork();

    if (pid == -1) {
        perror("Fork failed");
        return 0;
    }

    
    if (pid > 0) {
        close(pipefds[0]);

        printf("Parent: Sending message to child\n");
        write(pipefds[1], w_msg, strlen(w_msg) + 1); 
        close(pipefds[1]);  
    } 
    else {
        close(pipefds[1]);

        read(pipefds[0], r_msg, sizeof(r_msg));
        printf("Child: Received message: '%s'\n", r_msg);
        close(pipefds[0]);  
    }

    return 0;
}


/*
subham@subham-GF75:~/ProblemSheet II$ cc Q15.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out 
Parent: Sending message to child
Child: Received message: 'Hello from parent to child!'
*/