/* 
========================================================================================
Name : Q5.c
Author: Subham Sourav
Description :  Write a program to print the system limitation of
 a. maximum length of the arguments to the exec family of functions.
 b. maximum number of simultaneous process per user id.
 c. number of clock ticks (jiffy) per second.
 d. maximum number of open files
 e. size of a page
 f. total number of pages in the physical memory
 g. number of currently available pages in the physical memory. 
Date : 15-09-2024
========================================================================================
*/


#include <stdio.h>
#include <unistd.h>
#include <sys/sysinfo.h>
#include <limits.h>
#include <sys/resource.h>

int main() {
    long arg_max = sysconf(_SC_ARG_MAX);
    printf("Maximum length of arguments to exec: %ld\n", arg_max);

    long max_process = sysconf(_SC_CHILD_MAX);
    printf("Maximum number of processes per user id: %ld\n", max_process);

    long clock_ticks = sysconf(_SC_CLK_TCK);
    printf("Number of clock ticks per second: %ld\n", clock_ticks);

    struct rlimit limit;
    getrlimit(RLIMIT_NOFILE, &limit);
    printf("Maximum number of open files: %ld\n", limit.rlim_cur);

    long p_size = sysconf(_SC_PAGESIZE);
    printf("Size of a page: %ld bytes\n", p_size);

    long t_pages = sysconf(_SC_PHYS_PAGES);
    printf("Total number of pages in physical memory: %ld\n", t_pages);

    long avl_pages = sysconf(_SC_AVPHYS_PAGES);
    printf("Number of available pages in physical memory: %ld\n", avl_pages);

    return 0;
}


/*
subham@subham-GF75:~/ProblemSheet II$ cc Q5.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out 
Maximum length of arguments to exec: 2097152
Maximum number of processes per user id: 63053
Number of clock ticks per second: 100
Maximum number of open files: 1048576
Size of a page: 4096 bytes
Total number of pages in physical memory: 4053229
Number of available pages in physical memory: 2572901
*/