/* 
========================================================================================
Name : Q27.c
Author: Subham Sourav
Description :  Write a program to receive messages from the message queue.
 a. with 0 as a flag
 b. with IPC_NOWAIT as a flag
Date : 19-09-2024
========================================================================================
*/


#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/ipc.h>
#include <sys/msg.h>

struct message {
    long msg_type;
    char msg_text[100];
};

int main() {
    struct message msg;
    key_t key;
    int msgid;

    key = ftok("Notes.txt", 65);

    msgid = msgget(key, 0666 | IPC_CREAT);
    

    msgrcv(msgid, &msg, sizeof(msg), 1, IPC_NOWAIT);

    printf("Message received: %s\n", msg.msg_text);

    return 0;
}

/*
subham@subham-GF75:~/ProblemSheet II$ cc Q27b.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out
Message received: 
*/