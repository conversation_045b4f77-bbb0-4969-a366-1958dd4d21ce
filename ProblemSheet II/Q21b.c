/* 
========================================================================================
Name : Q21.c
Author: Subham Sourav
Description :  Write two programs so that both can communicate by FIFO -Use two way communications.
Date : 18-09-2024
========================================================================================
*/

#include <stdio.h>
#include <stdlib.h>
#include <fcntl.h>    
#include <sys/stat.h> 
#include <unistd.h>   
#include <string.h>   

#define FIFO1 "fifo1" 
#define FIFO2 "fifo2"  

int main() {
    char msg[1024], buf[1024];
    
    mkfifo(FIFO1, 0666); 
    mkfifo(FIFO2, 0666);

    while (1) {
        int r_fd = open(FIFO1, O_RDONLY);

        int w_fd = open(FIFO2, O_WRONLY);

        read(r_fd, buf, sizeof(buf));
        printf("Receiver received: %s\n", buf);
        close(r_fd);  

        printf("Receiver: Enter response to send: ");
        fgets(msg, 1024, stdin);

        write(w_fd, msg, strlen(msg) + 1);
        close(w_fd);  
    }

    return 0;
}
/*
subham@subham-GF75:~/ProblemSheet II$ cc Q21b.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out
Receiver received: Hi from sender

Receiver: Enter response to send: hi from receiver
Receiver received: should we exit?

Receiver: Enter response to send: yes.
^C
*/