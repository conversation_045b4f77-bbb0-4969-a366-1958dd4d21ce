/* 
========================================================================================
Name : Q13.c
Author: Subham Sourav
Description :   Write two programs: first program is waiting to catch SIGSTOP signal, the second program
will send the signal (using kill system call). Find out whether the first program is able to catch
the signal or not.
Date : 17-09-2024
========================================================================================
*/

// sender.c
#include <stdio.h>
#include <signal.h>
#include <unistd.h>
#include <stdlib.h>

int main(int argc, char *argv[]) {
    if (argc != 2) {
        perror("argument error");
    }

    pid_t receiver_pid = atoi(argv[1]);  

    printf("Sending SIGSTOP to PID %d\n", receiver_pid);
    kill(receiver_pid, SIGSTOP); 

    sleep(2);  

    printf("Sending SIGCONT to PID %d\n", receiver_pid);
    kill(receiver_pid, SIGCONT);  

    return 0;
}
/*
subham@subham-GF75:~/ProblemSheet II$ cc -o z Q13b.c
subham@subham-GF75:~/ProblemSheet II$ ./z 34125
Sending SIGSTOP to PID 34125
Sending SIGCONT to PID 34125
*/