// sender.c
#include <stdio.h>
#include <stdlib.h>
#include <fcntl.h>    
#include <sys/stat.h> 
#include <unistd.h>  
#include <string.h> 


int main() {

 const char *FIFO_PATH= "my_fifo";
    mkfifo(FIFO_PATH, 0666);

    int fd = open(FIFO_PATH, O_WRONLY);

    char m[] = "Hello from the sender!";
    printf("Sending message: %s\n", m);

    write(fd, m, strlen(m) + 1); 

    
    close(fd);

    return 0;
}
/*
subham@subham-GF75:~/ProblemSheet II$ cc Q20a.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out 
Sending message: Hello from the sender!
*/