     /* 
========================================================================================
Name : Q17.c
Author: Subham Sourav
Description :  Write a program to execute ls -l | wc.
 a. use dup
 b. use dup2
 c. use fcntl
Date : 17-09-2024
========================================================================================
*/
   
    #include <stdio.h>
#include <unistd.h>
#include <stdlib.h>
#include <sys/types.h>
#include <sys/wait.h>

int main() {
    int pipefd[2]; 
    pid_t pid;

    pipe(pipefd)  ;
        
    pid = fork();

    if (pid == 0) {

        close(pipefd[0]);

        int new_fd = dup2(pipefd[1],1);
        execlp("ls", "ls", "-l", NULL);

    } else {
        
        close(pipefd[1]);

        int new_fd = dup2(pipefd[0],0);

        close(pipefd[0]);
        execlp("wc", "wc", NULL);

    }

    return 0;
}
/*
subham@subham-GF75:~/ProblemSheet II$ cc Q17b.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out 
     34     299    1727
*/