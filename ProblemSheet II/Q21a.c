/* 
========================================================================================
Name : Q21.c
Author: Subham Sourav
Description :  Write two programs so that both can communicate by FIFO -Use two way communications.
Date : 18-09-2024
========================================================================================
*/


#include <stdio.h>
#include <stdlib.h>
#include <fcntl.h>   
#include <sys/stat.h>
#include <unistd.h>   
#include <string.h>   

#define FIFO1 "fifo1"  
#define FIFO2 "fifo2"  

int main() {
    char msg[1024], buf[1024];
    
    mkfifo(FIFO1, 0666);  
    mkfifo(FIFO2, 0666);  

    while (1) {
        int w_fd = open(FIFO1, O_WRONLY);

        int r_fd = open(FIFO2, O_RDONLY);

        printf("Sender: Enter msg to send: ");
        fgets(msg, 1024, stdin);

        
        write(w_fd, msg, strlen(msg) + 1);  
        close(w_fd);  

        
        read(r_fd, buf, sizeof(buf));
        printf("Sender received: %s\n", buf);
        close(r_fd);  
    }

    return 0;
}
/*
subham@subham-GF75:~/ProblemSheet II$ cc Q21a.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out
Sender: Enter msg to send: Hi from sender
Sender received: hi from receiver

Sender: Enter msg to send: should we exit?
Sender received: yes.

Sender: Enter msg to send: ^C
*/