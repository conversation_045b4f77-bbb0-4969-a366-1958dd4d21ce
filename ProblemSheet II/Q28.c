/* 
========================================================================================
Name : Q28.c
Author: Subham Sourav
Description :  Write a program to change the exiting message queue permission. (use msqid_ds structure)
Date : 19-09-2024
========================================================================================
*/


#include <stdio.h>
#include <stdlib.h>
#include <sys/ipc.h>
#include <sys/msg.h>
#include <sys/types.h>

int main() {
    key_t key;
    int msgid;
    struct msqid_ds buf;

    key = ftok("progfile", 65);

    msgid = msgget(key, 0666 | IPC_CREAT);
   

    msgctl(msgid, IPC_STAT, &buf) ;

    printf("Current permissions: %o\n", buf.msg_perm.mode);

    buf.msg_perm.mode = 0660;

   msgctl(msgid, IPC_SET, &buf) ;

    printf("Permissions changed to: %o\n", buf.msg_perm.mode);

    return 0;
}
/*
subham@subham-GF75:~/ProblemSheet II$ cc Q28.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out
Current permissions: 666
Permissions changed to: 660
*/
