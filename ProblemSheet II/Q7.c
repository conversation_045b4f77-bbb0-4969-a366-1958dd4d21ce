/* 
========================================================================================
Name : Q7.c
Author: Subham Sourav
Description :   Write a simple program to print the created thread ids. 
Date : 15-09-2024
========================================================================================
*/

#include <stdio.h>
#include <pthread.h>
#include <unistd.h>

void* print_threadid(void* arg) {
    int s=*(int *)arg;
    printf("Thread %d ID: %lu\n", s,pthread_self());
    return NULL;
}

int main() {
    int n = 5;  
    pthread_t threads[n];
    int th[n];

    
    for (int i = 0; i < n; i++) {
        th[i]=i+1;
        if (pthread_create(&threads[i], NULL, print_threadid, &th[i]) != 0) {
            perror("Failed to create thread");
        }
    }

    // Wait for all threads to finish
    for (int i = 0; i < n; i++) {
        pthread_join(threads[i], NULL);
    }

    return 0;
}

/*
subham@subham-GF75:~/ProblemSheet II$ cc Q7.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out 
Thread 1 ID: 133053548594880
Thread 2 ID: 133053538109120
Thread 4 ID: 133053387114176
Thread 3 ID: 133053527623360
Thread 5 ID: 133053517137600
*/