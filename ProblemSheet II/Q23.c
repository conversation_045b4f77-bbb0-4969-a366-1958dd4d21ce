/* 
========================================================================================
Name : Q23  .c
Author: Subham Sourav
Description :  . Write a program to print the maximum number of files can be opened within a process and
size of a pipe (circular buffer).
Date : 18-09-2024
========================================================================================
*/

#include <fcntl.h>
#include <sys/types.h>
#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <sys/resource.h>
#include <sys/stat.h>


int main() {
    struct rlimit limit;
    int pipefd[2];
    int pip_size;

    getrlimit(RLIMIT_NOFILE, &limit) ;

    printf("Maximum number of files that can be opened by this process: %ld\n", limit.rlim_cur);

    pipe(pipefd) == -1 ;

   pip_size = pathconf(".",_PC_PIPE_BUF);
    

    printf("Size of the pipe's circular buffer: %d bytes\n",pip_size);

    close(pipefd[0]);
    close(pipefd[1]);

    return 0;
}
/*
subham@subham-GF75:~/ProblemSheet II$ cc Q23.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out
Maximum number of files that can be opened by this process: 1048576
Size of the pipe's circular buffer: 4096 bytes
*/