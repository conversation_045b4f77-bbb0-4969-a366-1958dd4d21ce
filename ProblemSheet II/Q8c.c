/* 
========================================================================================
Name : Q8.c
Author: Subham Sourav
Description :    Write a separate program using signal system call to catch the following signals.
 a. SIGSEGV
 b. SIGINT
 c. SIGFPE
 d. SIGALRM (use alarm system call)
 e. SIGALRM (use setitimer system call)
 f. SIGVTALRM (use setitimer system call)
 g. SIGPROF (use setitimer system call)
Date : 16-09-2024
========================================================================================
*/
#include <stdio.h>
#include <signal.h>
#include <stdlib.h>

void handle(int s) {
    printf("Caught SIGFPE \n");
    exit(1);
}

int main() {
    signal(SIGFPE, handle);

    int a = 1;
    int b = 0;
    int c = a / b;

    return 0;
}

/*
subham@subham-GF75:~/ProblemSheet II$ cc Q8c.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out 
Caught SIGFPE 
*/