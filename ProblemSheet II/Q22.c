/* 
========================================================================================
Name : Q22.c
Author: Subham Sourav
Description :  Write a program to wait for data to be written into FIFO within 10 seconds, use select
system call with FIFO.
Date : 18-09-2024
========================================================================================
*/

#include <stdio.h>
#include <stdlib.h>
#include <fcntl.h>
#include <sys/select.h>
#include <sys/stat.h>
#include <unistd.h>

#define FIFO_PATH "my_fifo"

int main() {
    int fd;
    char buf[1024];
    fd_set read_fds;
    struct timeval timeout;
    
    mkfifo(FIFO_PATH, 0666) ;

    
    fd = open(FIFO_PATH, O_RDONLY | O_NONBLOCK);
    

    
    FD_SET(fd, &read_fds);   

    timeout.tv_sec = 10;          
    timeout.tv_usec = 0;         

    
    int ret = select(fd + 1, &read_fds, NULL, NULL, &timeout);
    
    if (ret == -1) {

        exit(0);

    } else if (ret == 0) {

        printf("Timeout: No data written to FIFO within 10 seconds.\n");
    } else {

            int bytes_read = read(fd, buf, sizeof(buf) - 1);
            if (bytes_read > 0) {
                buf[bytes_read] = '\0';  
                printf("Data read from FIFO: %s\n", buf);
            } 
        
    }

    
    close(fd);

    return 0;
}
/*
Terminal I
subham@subham-GF75:~/ProblemSheet II$ cc Q22.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out
Timeout: No data written to FIFO within 10 seconds.
subham@subham-GF75:~/ProblemSheet II$ ./a.out
Data read from FIFO: Hello from another process!


Terminal II
subham@subham-GF75:~/ProblemSheet II$ 
subham@subham-GF75:~/ProblemSheet II$ echo "Hello from another process!" > my_fifo

*/