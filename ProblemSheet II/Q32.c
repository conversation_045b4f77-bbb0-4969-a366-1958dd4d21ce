/* 
========================================================================================
Name : Q32.c
Author: Subham Sourav
Description : . Write a program to implement semaphore to protect any critical section.
 a. rewrite the ticket number creation program using semaphore
 b. protect shared memory from concurrent write access
 c. protect multiple pseudo resources ( may be two) using counting semaphore
 d. remove the created semaphore 
Date : 22-09-2024
========================================================================================
*/

#include <stdio.h>
#include <stdlib.h>
#include <pthread.h>
#include <semaphore.h>
#include <unistd.h>

// Shared data
int ticket_number = 0;
int shared_memory = 0;

// Semaphore declarations
sem_t ticket_sem;   // Binary semaphore for ticket generation
sem_t memory_sem;   // Binary semaphore for shared memory protection
sem_t resource_sem; // Counting semaphore for protecting pseudo-resources (2 resources)

// Function to generate ticket number (Protected by semaphore)
void* generate_ticket(void* arg) {
    sem_wait(&ticket_sem);  // Wait to enter critical section
    ticket_number++;
    printf("Generated ticket number: %d\n", ticket_number);
    sem_post(&ticket_sem);  // Exit critical section
    return NULL;
}

// Function to write to shared memory (Protected by semaphore)
void* write_shared_memory(void* arg) {
    sem_wait(&memory_sem);  // Wait to enter critical section
    shared_memory++;
    printf("Written to shared memory: %d\n", shared_memory);
    sem_post(&memory_sem);  // Exit critical section
    return NULL;
}

// Function to access pseudo-resource (Protected by counting semaphore)
void* access_resource(void* arg) {
    sem_wait(&resource_sem);  // Wait for available resource
    printf("Accessing a pseudo-resource.\n");
    sleep(1); // Simulate some operation on the resource
    printf("Releasing the pseudo-resource.\n");
    sem_post(&resource_sem);  // Release the resource
    return NULL;
}

// Main function to initialize semaphores and create threads
int main() {
    // Initialize semaphores
    sem_init(&ticket_sem, 0, 1);  // Binary semaphore for ticket creation
    sem_init(&memory_sem, 0, 1);  // Binary semaphore for shared memory
    sem_init(&resource_sem, 0, 2);  // Counting semaphore for two pseudo-resources

    pthread_t threads[10];

    // Create threads for ticket generation
    for (int i = 0; i < 3; i++) {
        pthread_create(&threads[i], NULL, generate_ticket, NULL);
    }

    // Create threads for writing to shared memory
    for (int i = 3; i < 6; i++) {
        pthread_create(&threads[i], NULL, write_shared_memory, NULL);
    }

    // Create threads to access pseudo-resources
    for (int i = 6; i < 10; i++) {
        pthread_create(&threads[i], NULL, access_resource, NULL);
    }

    // Join threads
    for (int i = 0; i < 10; i++) {
        pthread_join(threads[i], NULL);
    }

    // Destroy semaphores
    sem_destroy(&ticket_sem);
    sem_destroy(&memory_sem);
    sem_destroy(&resource_sem);

    return 0;
}
/*
subham@subham-GF75:~/ProblemSheet II$ cc Q32.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out
Generated ticket number: 1
Generated ticket number: 2
Generated ticket number: 3
Written to shared memory: 1
Written to shared memory: 2
Written to shared memory: 3
Accessing a pseudo-resource.
Accessing a pseudo-resource.
Releasing the pseudo-resource.
Accessing a pseudo-resource.
Releasing the pseudo-resource.
Accessing a pseudo-resource.
Releasing the pseudo-resource.
Releasing the pseudo-resource.
*/