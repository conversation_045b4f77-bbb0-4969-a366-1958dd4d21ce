/* 
========================================================================================
Name : Q12.c
Author: Subham Sourav
Description :   Write a program to create an orphan process. Use kill system call to send SIGKILL signal to
the parent process from the child process.
Date : 17-09-2024
========================================================================================
*/

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>

int main() {
    pid_t pid = fork();  

    if (pid < 0) {
        perror("Fork failed");
    }

    else if (pid == 0) {
        // Child process
        printf("Child: My PID is %d. My parent's PID is %d\n", getpid(), getppid());
        sleep(2);  

        printf("Child: Sending SIGKILL to parent (PID %d)\n", getppid());
        kill(getppid(), SIGKILL);

        sleep(1);

        printf("Child: My new parent's PID is %d \n", getppid());
    }
    else {
        // Parent process
        printf("Parent: My PID is %d. My child's PID is %d\n", getpid(), pid);
        
        sleep(10);  
    }

    return 0;
}
/*
subham@subham-GF75:~/ProblemSheet II$ cc Q12.c 
subham@subham-GF75:~/ProblemSheet II$ ./a.out 
Parent: My PID is 33869. My child's PID is 33870
Child: My PID is 33870. My parent's PID is 33869
Child: Sending SIGKILL to parent (PID 33869)
Killed
My new parent's PID is 2026 
*/