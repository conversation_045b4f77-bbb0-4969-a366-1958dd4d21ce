/* 
========================================================================================
Name : Q33.c
Author: Subham Sourav
Description : Write a program to communicate between two machines using socket.
Date : 23-09-2024
========================================================================================
*/

//server
#include<stdio.h>
#include<sys/socket.h>
#include<netinet/in.h>
#include<unistd.h>

int main(){

    struct sockaddr_in serv, cli;//Structure in netinet/in.h

    int sd, sz, nsd;
    char buff[50];

    sd = socket(AF_UNIX, SOCK_STREAM, 0); 

    serv.sin_family = AF_UNIX;
    serv.sin_addr.s_addr = INADDR_ANY; //with INADDR_ANY it can serve any address
    serv.sin_port = htons(5055); //htons converts little endian to big endian type

    bind(sd, (void *)&serv, sizeof(serv));

    listen(sd, 5);//Limit for second argument is from 1 to 5 because at a time 

    sz = sizeof(cli);
    nsd = accept(sd, (void*)(&cli), &sz);
    read(nsd, buff, sizeof(buff));
    printf("Message from Client: %s\n", buff);
    write(nsd, "ACK from Server\n", 17);
}
/*
subham@subham-GF75:~/ProblemSheet II$ Q33a.c
Q33a.c: command not found
subham@subham-GF75:~/ProblemSheet II$ cc Q33a.c
subham@subham-GF75:~/ProblemSheet II$ ./a.out 
Message from Client: Hello Server
*/